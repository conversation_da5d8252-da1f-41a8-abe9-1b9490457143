{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,4SAAC,iRAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,iRAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,4SAAC,gRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,4SAAC,gRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,gRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,gRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/profile/usage/components/usage-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts';\n\ninterface UsageMetric {\n  date: Date;\n  cubentUnitsUsed: number;\n  requestsMade: number;\n}\n\ninterface UsageChartProps {\n  data: UsageMetric[];\n}\n\nexport function UsageChart({ data }: UsageChartProps) {\n  const chartData = useMemo(() => {\n    // Fill in missing days with zero values for the last 30 days\n    const today = new Date();\n    const thirtyDaysAgo = new Date(today);\n    thirtyDaysAgo.setDate(today.getDate() - 29);\n\n    const filledData = [];\n    for (let i = 0; i < 30; i++) {\n      const currentDate = new Date(thirtyDaysAgo);\n      currentDate.setDate(thirtyDaysAgo.getDate() + i);\n\n      const existingData = data.find(metric => {\n        const metricDate = new Date(metric.date);\n        return metricDate.toDateString() === currentDate.toDateString();\n      });\n\n      filledData.push({\n        date: currentDate.toLocaleDateString('en-US', {\n          month: 'short',\n          day: 'numeric'\n        }),\n        fullDate: currentDate.toLocaleDateString('en-US', {\n          weekday: 'short',\n          month: 'short',\n          day: 'numeric'\n        }),\n        cubentUnits: existingData?.cubentUnitsUsed || 0,\n        requests: existingData?.requestsMade || 0,\n      });\n    }\n\n    return filledData;\n  }, [data]);\n\n  // Custom tooltip component\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <div className=\"bg-gray-900 dark:bg-gray-800 text-white p-3 rounded-lg shadow-lg border border-gray-700\">\n          <p className=\"font-medium text-center mb-2\">{data.fullDate}</p>\n          <div className=\"space-y-1\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\n              <span className=\"text-sm\">{data.cubentUnits.toFixed(2)} Cubent Units</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\n              <span className=\"text-sm\">{data.requests} Messages</span>\n            </div>\n          </div>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"h-80 w-full\">\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <BarChart\n            data={chartData}\n            margin={{\n              top: 20,\n              right: 30,\n              left: 20,\n              bottom: 60,\n            }}\n          >\n            <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\n            <XAxis\n              dataKey=\"date\"\n              tick={{ fontSize: 12 }}\n              angle={-45}\n              textAnchor=\"end\"\n              height={60}\n              interval={Math.floor(chartData.length / 8)} // Show every ~4th label to avoid crowding\n            />\n            <YAxis\n              tick={{ fontSize: 12 }}\n              label={{ value: 'Usage', angle: -90, position: 'insideLeft' }}\n            />\n            <Tooltip content={<CustomTooltip />} />\n            <Legend />\n            <Bar\n              dataKey=\"cubentUnits\"\n              name=\"Cubent Units\"\n              fill=\"#3b82f6\"\n              radius={[2, 2, 0, 0]}\n            />\n            <Bar\n              dataKey=\"requests\"\n              name=\"Messages\"\n              fill=\"#10b981\"\n              radius={[2, 2, 0, 0]}\n            />\n          </BarChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAeO,SAAS,WAAW,EAAE,IAAI,EAAmB;;IAClD,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;yCAAE;YACxB,6DAA6D;YAC7D,MAAM,QAAQ,IAAI;YAClB,MAAM,gBAAgB,IAAI,KAAK;YAC/B,cAAc,OAAO,CAAC,MAAM,OAAO,KAAK;YAExC,MAAM,aAAa,EAAE;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,MAAM,cAAc,IAAI,KAAK;gBAC7B,YAAY,OAAO,CAAC,cAAc,OAAO,KAAK;gBAE9C,MAAM,eAAe,KAAK,IAAI;kEAAC,CAAA;wBAC7B,MAAM,aAAa,IAAI,KAAK,OAAO,IAAI;wBACvC,OAAO,WAAW,YAAY,OAAO,YAAY,YAAY;oBAC/D;;gBAEA,WAAW,IAAI,CAAC;oBACd,MAAM,YAAY,kBAAkB,CAAC,SAAS;wBAC5C,OAAO;wBACP,KAAK;oBACP;oBACA,UAAU,YAAY,kBAAkB,CAAC,SAAS;wBAChD,SAAS;wBACT,OAAO;wBACP,KAAK;oBACP;oBACA,aAAa,cAAc,mBAAmB;oBAC9C,UAAU,cAAc,gBAAgB;gBAC1C;YACF;YAEA,OAAO;QACT;wCAAG;QAAC;KAAK;IAET,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAE,WAAU;kCAAgC,KAAK,QAAQ;;;;;;kCAC1D,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;;;;;kDACf,4SAAC;wCAAK,WAAU;;4CAAW,KAAK,WAAW,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAEzD,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;;;;;kDACf,4SAAC;wCAAK,WAAU;;4CAAW,KAAK,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;QAKnD;QACA,OAAO;IACT;IAEA,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC,ySAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAO;0BACvC,cAAA,4SAAC,0RAAA,CAAA,WAAQ;oBACP,MAAM;oBACN,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,MAAM;wBACN,QAAQ;oBACV;;sCAEA,4SAAC,mSAAA,CAAA,gBAAa;4BAAC,iBAAgB;4BAAM,WAAU;;;;;;sCAC/C,4SAAC,2RAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,MAAM;gCAAE,UAAU;4BAAG;4BACrB,OAAO,CAAC;4BACR,YAAW;4BACX,QAAQ;4BACR,UAAU,KAAK,KAAK,CAAC,UAAU,MAAM,GAAG;;;;;;sCAE1C,4SAAC,2RAAA,CAAA,QAAK;4BACJ,MAAM;gCAAE,UAAU;4BAAG;4BACrB,OAAO;gCAAE,OAAO;gCAAS,OAAO,CAAC;gCAAI,UAAU;4BAAa;;;;;;sCAE9D,4SAAC,6RAAA,CAAA,UAAO;4BAAC,uBAAS,4SAAC;;;;;;;;;;sCACnB,4SAAC,4RAAA,CAAA,SAAM;;;;;sCACP,4SAAC,yRAAA,CAAA,MAAG;4BACF,SAAQ;4BACR,MAAK;4BACL,MAAK;4BACL,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;;;;;;sCAEtB,4SAAC,yRAAA,CAAA,MAAG;4BACF,SAAQ;4BACR,MAAK;4BACL,MAAK;4BACL,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GAvGgB;KAAA", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/profile/usage/components/usage-analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@repo/design-system/components/ui/card';\nimport { Badge } from '@repo/design-system/components/ui/badge';\nimport { Progress } from '@repo/design-system/components/ui/progress';\nimport { Avatar, AvatarFallback, AvatarImage } from '@repo/design-system/components/ui/avatar';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/design-system/components/ui/tabs';\nimport {\n  ArrowLeft,\n  Zap,\n  MessageSquare,\n  TrendingUp,\n  RefreshCw,\n  Crown,\n  Sparkles,\n  Download\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { UsageChart } from './usage-chart';\n\ninterface UsageData {\n  totalCubentUnits: number;\n  totalMessages: number;\n  userLimit: number;\n  subscriptionTier: string;\n  chartData: Array<{\n    date: Date;\n    cubentUnitsUsed: number;\n    requestsMade: number;\n  }>;\n  user: {\n    name: string;\n    email: string;\n    picture?: string;\n  };\n}\n\ninterface UsageAnalyticsProps {\n  initialData: UsageData;\n}\n\nexport function UsageAnalytics({ initialData }: UsageAnalyticsProps) {\n  const [data, setData] = useState(initialData);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(new Date());\n\n  const refreshData = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await fetch('/api/extension/usage/stats', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success) {\n          setData(prev => ({\n            ...prev,\n            totalCubentUnits: result.totalCubentUnits,\n            totalMessages: result.totalMessages,\n            userLimit: result.userLimit,\n            subscriptionTier: result.subscriptionTier,\n          }));\n          setLastUpdated(new Date());\n        }\n      }\n    } catch (error) {\n      console.error('Failed to refresh usage data:', error);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  // Auto-refresh every 30 seconds\n  useEffect(() => {\n    const interval = setInterval(refreshData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const usagePercentage = (data.totalCubentUnits / data.userLimit) * 100;\n  const isNearLimit = usagePercentage > 80;\n  const isOverLimit = usagePercentage > 100;\n\n  const getTierInfo = (tier: string) => {\n    switch (tier) {\n      case 'pro':\n        return { name: 'Pro', icon: Crown, color: 'text-yellow-600' };\n      case 'premium':\n        return { name: 'Premium', icon: Sparkles, color: 'text-purple-600' };\n      default:\n        return { name: 'Free Trial', icon: Zap, color: 'text-blue-600' };\n    }\n  };\n\n  const tierInfo = getTierInfo(data.subscriptionTier);\n  const TierIcon = tierInfo.icon;\n\n  return (\n    <div className=\"relative\">\n      {/* Grey overlay to make page appear unavailable but still functional */}\n      <div className=\"absolute inset-0 bg-gray-500 bg-opacity-30 z-10 pointer-events-none\" />\n\n      <div className=\"relative z-0\">\n      {/* Shadcn-Admin Style Header */}\n      <div className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\n        <div className=\"flex items-center gap-2 px-4\">\n          <Button variant=\"ghost\" size=\"sm\" asChild>\n            <Link href=\"/profile\">\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Profile\n            </Link>\n          </Button>\n        </div>\n        <div className=\"ml-auto flex items-center gap-2 px-4\">\n          <div className=\"text-right\">\n            <p className=\"text-xs text-muted-foreground\">Last updated</p>\n            <p className=\"text-sm font-medium\">\n              {lastUpdated.toLocaleTimeString()}\n            </p>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={refreshData}\n            disabled={isRefreshing}\n          >\n            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />\n            Refresh\n          </Button>\n          <Button size=\"sm\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Download\n          </Button>\n        </div>\n      </div>\n\n      {/* Shadcn-Admin Style Main Content */}\n      <main className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\n        <div className=\"flex items-center justify-between space-y-2\">\n          <div>\n            <h2 className=\"text-2xl font-bold tracking-tight\">Usage Analytics</h2>\n            <p className=\"text-muted-foreground\">\n              Here's what happening with your usage today\n            </p>\n          </div>\n        </div>\n\n        {/* Shadcn-Admin Style Stats Cards */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          {/* Cubent Units Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total Cubent Units\n              </CardTitle>\n              <Zap className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.totalCubentUnits.toFixed(2)}</div>\n              <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                <span className={`font-medium ${isOverLimit ? 'text-red-500' : isNearLimit ? 'text-yellow-500' : 'text-green-500'}`}>\n                  {usagePercentage.toFixed(0)}%\n                </span>\n                <span>of {data.userLimit} limit</span>\n              </div>\n              <Progress\n                value={Math.min(usagePercentage, 100)}\n                className=\"mt-2 h-1\"\n              />\n            </CardContent>\n          </Card>\n\n          {/* Messages Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Messages</CardTitle>\n              <MessageSquare className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.totalMessages.toLocaleString()}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +{Math.round((data.totalMessages / 30) * 7)} this week\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Efficiency Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Efficiency</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {data.totalMessages > 0 ? (data.totalCubentUnits / data.totalMessages).toFixed(2) : '0.00'}\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                units per message\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Subscription Card */}\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Subscription</CardTitle>\n              <TierIcon className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.userLimit}</div>\n              <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  <TierIcon className={`h-3 w-3 mr-1 ${tierInfo.color}`} />\n                  {tierInfo.name}\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Shadcn-Admin Style Chart with Tabs */}\n        <Tabs defaultValue=\"overview\" className=\"space-y-4\">\n          <TabsList>\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n            <TabsTrigger value=\"reports\">Reports</TabsTrigger>\n          </TabsList>\n          <TabsContent value=\"overview\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Usage Overview</CardTitle>\n                <CardDescription>\n                  Daily consumption for the last 30 days\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"pl-2\">\n                <UsageChart data={data.chartData} />\n              </CardContent>\n            </Card>\n          </TabsContent>\n          <TabsContent value=\"analytics\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Detailed Analytics</CardTitle>\n                <CardDescription>\n                  Advanced usage metrics and trends\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  Advanced analytics coming soon...\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n          <TabsContent value=\"reports\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Usage Reports</CardTitle>\n                <CardDescription>\n                  Export and download usage reports\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  Report generation coming soon...\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n\n        {/* Shadcn-Admin Style Upgrade Prompt */}\n        {(isNearLimit || isOverLimit) && data.subscriptionTier === 'free_trial' && (\n          <Card className=\"border-yellow-200 bg-yellow-50 dark:border-yellow-800/50 dark:bg-yellow-900/10\">\n            <CardContent className=\"flex items-center justify-between p-6\">\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100 dark:bg-yellow-900/30\">\n                  <Crown className=\"h-6 w-6 text-yellow-600 dark:text-yellow-400\" />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold\">\n                    {isOverLimit ? 'Usage Limit Exceeded' : 'Approaching Usage Limit'}\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Upgrade to Pro for unlimited Cubent Units and advanced features.\n                  </p>\n                </div>\n              </div>\n              <Button>\n                <Sparkles className=\"h-4 w-4 mr-2\" />\n                Upgrade Now\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AApBA;;;;;;;;;;AA2CO,SAAS,eAAe,EAAE,WAAW,EAAuB;;IACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAEnD,MAAM,cAAc;QAClB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,IAAI,OAAO,OAAO,EAAE;oBAClB,QAAQ,CAAA,OAAQ,CAAC;4BACf,GAAG,IAAI;4BACP,kBAAkB,OAAO,gBAAgB;4BACzC,eAAe,OAAO,aAAa;4BACnC,WAAW,OAAO,SAAS;4BAC3B,kBAAkB,OAAO,gBAAgB;wBAC3C,CAAC;oBACD,eAAe,IAAI;gBACrB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,gCAAgC;IAChC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW,YAAY,aAAa;YAC1C;4CAAO,IAAM,cAAc;;QAC7B;mCAAG,EAAE;IAEL,MAAM,kBAAkB,AAAC,KAAK,gBAAgB,GAAG,KAAK,SAAS,GAAI;IACnE,MAAM,cAAc,kBAAkB;IACtC,MAAM,cAAc,kBAAkB;IAEtC,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAO,MAAM,2RAAA,CAAA,QAAK;oBAAE,OAAO;gBAAkB;YAC9D,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,MAAM,iSAAA,CAAA,WAAQ;oBAAE,OAAO;gBAAkB;YACrE;gBACE,OAAO;oBAAE,MAAM;oBAAc,MAAM,uRAAA,CAAA,MAAG;oBAAE,OAAO;gBAAgB;QACnE;IACF;IAEA,MAAM,WAAW,YAAY,KAAK,gBAAgB;IAClD,MAAM,WAAW,SAAS,IAAI;IAE9B,qBACE,4SAAC;QAAI,WAAU;;0BAEb,4SAAC;gBAAI,WAAU;;;;;;0BAEf,4SAAC;gBAAI,WAAU;;kCAEf,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC,8JAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,OAAO;8CACvC,cAAA,4SAAC,8QAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,4SAAC,uSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAK5C,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,4SAAC;gDAAE,WAAU;0DACV,YAAY,kBAAkB;;;;;;;;;;;;kDAGnC,4SAAC,8JAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;;0DAEV,4SAAC,uSAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,aAAa,EAAE,eAAe,iBAAiB,IAAI;;;;;;4CAAI;;;;;;;kDAGhF,4SAAC,8JAAA,CAAA,SAAM;wCAAC,MAAK;;0DACX,4SAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAO3C,4SAAC;wBAAK,WAAU;;0CACd,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;;sDACC,4SAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAOzC,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC,4JAAA,CAAA,OAAI;;0DACH,4SAAC,4JAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,4SAAC,4JAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;kEAG3C,4SAAC,uRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;0DAEjB,4SAAC,4JAAA,CAAA,cAAW;;kEACV,4SAAC;wDAAI,WAAU;kEAAsB,KAAK,gBAAgB,CAAC,OAAO,CAAC;;;;;;kEACnE,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAK,WAAW,CAAC,YAAY,EAAE,cAAc,iBAAiB,cAAc,oBAAoB,kBAAkB;;oEAChH,gBAAgB,OAAO,CAAC;oEAAG;;;;;;;0EAE9B,4SAAC;;oEAAK;oEAAI,KAAK,SAAS;oEAAC;;;;;;;;;;;;;kEAE3B,4SAAC,gKAAA,CAAA,WAAQ;wDACP,OAAO,KAAK,GAAG,CAAC,iBAAiB;wDACjC,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,4SAAC,4JAAA,CAAA,OAAI;;0DACH,4SAAC,4JAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,4SAAC,4JAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;kEAC3C,4SAAC,+SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;0DAE3B,4SAAC,4JAAA,CAAA,cAAW;;kEACV,4SAAC;wDAAI,WAAU;kEAAsB,KAAK,aAAa,CAAC,cAAc;;;;;;kEACtE,4SAAC;wDAAE,WAAU;;4DAAgC;4DACzC,KAAK,KAAK,CAAC,AAAC,KAAK,aAAa,GAAG,KAAM;4DAAG;;;;;;;;;;;;;;;;;;;kDAMlD,4SAAC,4JAAA,CAAA,OAAI;;0DACH,4SAAC,4JAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,4SAAC,4JAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;kEAC3C,4SAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,4SAAC,4JAAA,CAAA,cAAW;;kEACV,4SAAC;wDAAI,WAAU;kEACZ,KAAK,aAAa,GAAG,IAAI,CAAC,KAAK,gBAAgB,GAAG,KAAK,aAAa,EAAE,OAAO,CAAC,KAAK;;;;;;kEAEtF,4SAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAOjD,4SAAC,4JAAA,CAAA,OAAI;;0DACH,4SAAC,4JAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,4SAAC,4JAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;kEAC3C,4SAAC;wDAAS,WAAU;;;;;;;;;;;;0DAEtB,4SAAC,4JAAA,CAAA,cAAW;;kEACV,4SAAC;wDAAI,WAAU;kEAAsB,KAAK,SAAS;;;;;;kEACnD,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC,6JAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;8EACnC,4SAAC;oEAAS,WAAW,CAAC,aAAa,EAAE,SAAS,KAAK,EAAE;;;;;;gEACpD,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQxB,4SAAC,4JAAA,CAAA,OAAI;gCAAC,cAAa;gCAAW,WAAU;;kDACtC,4SAAC,4JAAA,CAAA,WAAQ;;0DACP,4SAAC,4JAAA,CAAA,cAAW;gDAAC,OAAM;0DAAW;;;;;;0DAC9B,4SAAC,4JAAA,CAAA,cAAW;gDAAC,OAAM;0DAAY;;;;;;0DAC/B,4SAAC,4JAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;;;;;;;kDAE/B,4SAAC,4JAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;kDACtC,cAAA,4SAAC,4JAAA,CAAA,OAAI;;8DACH,4SAAC,4JAAA,CAAA,aAAU;;sEACT,4SAAC,4JAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,4SAAC,4JAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,4SAAC,4JAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,4SAAC,+LAAA,CAAA,aAAU;wDAAC,MAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;kDAItC,4SAAC,4JAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;kDACvC,cAAA,4SAAC,4JAAA,CAAA,OAAI;;8DACH,4SAAC,4JAAA,CAAA,aAAU;;sEACT,4SAAC,4JAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,4SAAC,4JAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,4SAAC,4JAAA,CAAA,cAAW;8DACV,cAAA,4SAAC;wDAAI,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;;;;;kDAM9D,4SAAC,4JAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;kDACrC,cAAA,4SAAC,4JAAA,CAAA,OAAI;;8DACH,4SAAC,4JAAA,CAAA,aAAU;;sEACT,4SAAC,4JAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,4SAAC,4JAAA,CAAA,kBAAe;sEAAC;;;;;;;;;;;;8DAInB,4SAAC,4JAAA,CAAA,cAAW;8DACV,cAAA,4SAAC;wDAAI,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAS/D,CAAC,eAAe,WAAW,KAAK,KAAK,gBAAgB,KAAK,8BACzD,4SAAC,4JAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,4SAAC,4JAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,4SAAC;;sEACC,4SAAC;4DAAG,WAAU;sEACX,cAAc,yBAAyB;;;;;;sEAE1C,4SAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAKjD,4SAAC,8JAAA,CAAA,SAAM;;8DACL,4SAAC,iSAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAtQgB;KAAA", "debugId": null}}]}