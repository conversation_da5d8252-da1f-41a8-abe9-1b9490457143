{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6VAAC,+QAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6VAAC,+QAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6VAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6VAAC,4SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6VAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6VAAC,+QAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6VAAC;;;;;8BACD,6VAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6VAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6VAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6VAAC;gBAAK,WAAU;0BACd,cAAA,6VAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6VAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6VAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6VAAC,+QAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6VAAC,+QAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,wSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6VAAC,+QAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,4SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/usage/tokens/components/token-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\n\ninterface TokenChartData {\n  date: string;\n  tokens: number;\n  inputTokens: number;\n  outputTokens: number;\n  requests: number;\n}\n\ninterface TokenChartProps {\n  data: TokenChartData[];\n}\n\nexport const TokenChart = ({ data }: TokenChartProps) => {\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`;\n    } else if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`;\n    }\n    return num.toLocaleString();\n  };\n\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-[#1a1a1a] border border-[#333] rounded-lg p-3 shadow-lg\">\n          <p className=\"text-white font-medium\">{label}</p>\n          <div className=\"space-y-1 mt-2\">\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 rounded-full bg-[#10b981]\" />\n              <span className=\"text-gray-300 text-sm\">Input Tokens:</span>\n              <span className=\"text-white font-medium\">{formatNumber(payload[0]?.value || 0)}</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 rounded-full bg-[#3b82f6]\" />\n              <span className=\"text-gray-300 text-sm\">Output Tokens:</span>\n              <span className=\"text-white font-medium\">{formatNumber(payload[1]?.value || 0)}</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <div className=\"w-3 h-3 rounded-full bg-[#d97706]\" />\n              <span className=\"text-gray-300 text-sm\">Total:</span>\n              <span className=\"text-white font-medium\">{formatNumber((payload[0]?.value || 0) + (payload[1]?.value || 0))}</span>\n            </div>\n          </div>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div className=\"h-80 w-full\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>\n          <defs>\n            <linearGradient id=\"inputTokensGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n              <stop offset=\"5%\" stopColor=\"#10b981\" stopOpacity={0.3} />\n              <stop offset=\"95%\" stopColor=\"#10b981\" stopOpacity={0} />\n            </linearGradient>\n            <linearGradient id=\"outputTokensGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n              <stop offset=\"5%\" stopColor=\"#3b82f6\" stopOpacity={0.3} />\n              <stop offset=\"95%\" stopColor=\"#3b82f6\" stopOpacity={0} />\n            </linearGradient>\n          </defs>\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#333\" className=\"opacity-50\" />\n          <XAxis \n            dataKey=\"date\" \n            axisLine={false}\n            tickLine={false}\n            tick={{ fill: '#9ca3af', fontSize: 12 }}\n            tickFormatter={(value) => {\n              const date = new Date(value);\n              return `${date.getMonth() + 1}/${date.getDate()}`;\n            }}\n          />\n          <YAxis \n            axisLine={false}\n            tickLine={false}\n            tick={{ fill: '#9ca3af', fontSize: 12 }}\n            tickFormatter={formatNumber}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Area\n            type=\"monotone\"\n            dataKey=\"inputTokens\"\n            stackId=\"1\"\n            stroke=\"#10b981\"\n            fill=\"url(#inputTokensGradient)\"\n            strokeWidth={2}\n          />\n          <Area\n            type=\"monotone\"\n            dataKey=\"outputTokens\"\n            stackId=\"1\"\n            stroke=\"#3b82f6\"\n            fill=\"url(#outputTokensGradient)\"\n            strokeWidth={2}\n          />\n        </AreaChart>\n      </ResponsiveContainer>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAgBO,MAAM,aAAa,CAAC,EAAE,IAAI,EAAmB;IAClD,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,cAAc;IAC3B;IAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAE,WAAU;kCAA0B;;;;;;kCACvC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;;;;;kDACf,6VAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6VAAC;wCAAK,WAAU;kDAA0B,aAAa,OAAO,CAAC,EAAE,EAAE,SAAS;;;;;;;;;;;;0CAE9E,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;;;;;kDACf,6VAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6VAAC;wCAAK,WAAU;kDAA0B,aAAa,OAAO,CAAC,EAAE,EAAE,SAAS;;;;;;;;;;;;0CAE9E,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;;;;;kDACf,6VAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6VAAC;wCAAK,WAAU;kDAA0B,aAAa,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;QAKnH;QACA,OAAO;IACT;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC,sSAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6VAAC,wRAAA,CAAA,YAAS;gBAAC,MAAM;gBAAM,QAAQ;oBAAE,KAAK;oBAAI,OAAO;oBAAI,MAAM;oBAAG,QAAQ;gBAAE;;kCACtE,6VAAC;;0CACC,6VAAC;gCAAe,IAAG;gCAAsB,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAI,IAAG;;kDAC/D,6VAAC;wCAAK,QAAO;wCAAK,WAAU;wCAAU,aAAa;;;;;;kDACnD,6VAAC;wCAAK,QAAO;wCAAM,WAAU;wCAAU,aAAa;;;;;;;;;;;;0CAEtD,6VAAC;gCAAe,IAAG;gCAAuB,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAI,IAAG;;kDAChE,6VAAC;wCAAK,QAAO;wCAAK,WAAU;wCAAU,aAAa;;;;;;kDACnD,6VAAC;wCAAK,QAAO;wCAAM,WAAU;wCAAU,aAAa;;;;;;;;;;;;;;;;;;kCAGxD,6VAAC,gSAAA,CAAA,gBAAa;wBAAC,iBAAgB;wBAAM,QAAO;wBAAO,WAAU;;;;;;kCAC7D,6VAAC,wRAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,MAAM;4BAAE,MAAM;4BAAW,UAAU;wBAAG;wBACtC,eAAe,CAAC;4BACd,MAAM,OAAO,IAAI,KAAK;4BACtB,OAAO,GAAG,KAAK,QAAQ,KAAK,EAAE,CAAC,EAAE,KAAK,OAAO,IAAI;wBACnD;;;;;;kCAEF,6VAAC,wRAAA,CAAA,QAAK;wBACJ,UAAU;wBACV,UAAU;wBACV,MAAM;4BAAE,MAAM;4BAAW,UAAU;wBAAG;wBACtC,eAAe;;;;;;kCAEjB,6VAAC,0RAAA,CAAA,UAAO;wBAAC,uBAAS,6VAAC;;;;;;;;;;kCACnB,6VAAC,uRAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,SAAQ;wBACR,QAAO;wBACP,MAAK;wBACL,aAAa;;;;;;kCAEf,6VAAC,uRAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,SAAQ;wBACR,QAAO;wBACP,MAAK;wBACL,aAAa;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/app/app/%28authenticated%29/usage/tokens/components/token-usage-content.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@repo/design-system/components/ui/card';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/design-system/components/ui/select';\nimport { TokenChart } from './token-chart';\nimport { Zap, TrendingUp, Calendar, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';\n\ninterface TokenUsageData {\n  totalTokens: number;\n  estimatedInputTokens: number;\n  estimatedOutputTokens: number;\n  avgDailyTokens: number;\n  peakDay: {\n    date: Date;\n    tokens: number;\n  };\n  chartData: Array<{\n    date: string;\n    tokens: number;\n    inputTokens: number;\n    outputTokens: number;\n    requests: number;\n  }>;\n  tokensByModel: Array<{\n    modelId: string;\n    tokens: number;\n    requests: number;\n    sessions: number;\n  }>;\n  recentUsage: Array<{\n    id: string;\n    modelId: string;\n    tokens: number;\n    requests: number;\n    cubentUnits: number;\n    timestamp: Date;\n    provider: string;\n  }>;\n  tokensPerRequest: number;\n}\n\ninterface TokenUsageContentProps {\n  data: TokenUsageData;\n}\n\nexport const TokenUsageContent = ({ data }: TokenUsageContentProps) => {\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    }).format(date);\n  };\n\n  const formatTime = (date: Date) => {\n    return new Intl.DateTimeFormat('en-US', {\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: true\n    }).format(date);\n  };\n\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return `${(num / 1000000).toFixed(1)}M`;\n    } else if (num >= 1000) {\n      return `${(num / 1000).toFixed(1)}K`;\n    }\n    return num.toLocaleString();\n  };\n\n  return (\n    <div className=\"space-y-6 p-6 bg-gray-800 min-h-screen\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-semibold text-white\">Token Usage</h1>\n          <p className=\"text-gray-400 mt-1\">Track your input and output token consumption</p>\n        </div>\n        <div className=\"flex items-center gap-3\">\n          <Select defaultValue=\"30d\">\n            <SelectTrigger className=\"w-32 bg-[#1a1a1a] border-[#333] text-white\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent className=\"bg-[#1a1a1a] border-[#333]\">\n              <SelectItem value=\"7d\" className=\"text-white hover:bg-[#333]\">Last 7 days</SelectItem>\n              <SelectItem value=\"30d\" className=\"text-white hover:bg-[#333]\">Last 30 days</SelectItem>\n              <SelectItem value=\"90d\" className=\"text-white hover:bg-[#333]\">Last 90 days</SelectItem>\n            </SelectContent>\n          </Select>\n          <Button variant=\"outline\" className=\"bg-[#1a1a1a] border-[#333] text-white hover:bg-[#333]\">\n            Export\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-400\">Total Tokens</CardTitle>\n            <Zap className=\"h-4 w-4 text-gray-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-white\">{formatNumber(data.totalTokens)}</div>\n            <p className=\"text-xs text-gray-400 mt-1\">All time usage</p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-400\">Input Tokens</CardTitle>\n            <ArrowUp className=\"h-4 w-4 text-green-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-white\">{formatNumber(data.estimatedInputTokens)}</div>\n            <p className=\"text-xs text-gray-400 mt-1\">~85% of total (estimated)</p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-400\">Output Tokens</CardTitle>\n            <ArrowDown className=\"h-4 w-4 text-blue-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-white\">{formatNumber(data.estimatedOutputTokens)}</div>\n            <p className=\"text-xs text-gray-400 mt-1\">~15% of total (estimated)</p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-400\">Daily Average</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-gray-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-white\">{formatNumber(data.avgDailyTokens)}</div>\n            <p className=\"text-xs text-gray-400 mt-1\">Tokens per day (7-day avg)</p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium text-gray-400\">Per Request</CardTitle>\n            <ArrowUpDown className=\"h-4 w-4 text-gray-400\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-white\">{formatNumber(data.tokensPerRequest)}</div>\n            <p className=\"text-xs text-gray-400 mt-1\">Average tokens/request</p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Token Usage Chart */}\n      <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n        <CardHeader>\n          <CardTitle className=\"text-white\">Token Consumption</CardTitle>\n          <p className=\"text-sm text-gray-400\">Daily token usage breakdown (input vs output)</p>\n        </CardHeader>\n        <CardContent>\n          <TokenChart data={data.chartData} />\n        </CardContent>\n      </Card>\n\n      {/* Two Column Layout */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Tokens by Model */}\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader>\n            <CardTitle className=\"text-white\">Tokens by Model</CardTitle>\n            <p className=\"text-sm text-gray-400\">Token consumption by AI model</p>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {data.tokensByModel.slice(0, 8).map((model, index) => (\n                <div key={model.modelId} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 rounded-full bg-[#d97706]\" />\n                    <div>\n                      <p className=\"text-sm font-medium text-white\">{model.modelId}</p>\n                      <p className=\"text-xs text-gray-400\">{model.requests} requests • {model.sessions} sessions</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-white\">{formatNumber(model.tokens)}</p>\n                    <p className=\"text-xs text-gray-400\">tokens</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Recent Token Usage */}\n        <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n          <CardHeader>\n            <CardTitle className=\"text-white\">Recent Usage</CardTitle>\n            <p className=\"text-sm text-gray-400\">Latest token consumption</p>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {data.recentUsage.slice(0, 8).map((usage) => (\n                <div key={usage.id} className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-white\">{usage.modelId}</p>\n                    <p className=\"text-xs text-gray-400\">\n                      {formatTime(usage.timestamp)} • {usage.provider}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-white\">{formatNumber(usage.tokens)}</p>\n                    <p className=\"text-xs text-gray-400\">{usage.requests} req • {usage.cubentUnits} units</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Note about estimation */}\n      <Card className=\"bg-[#1a1a1a] border border-[#333]\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-start gap-3\">\n            <div className=\"w-2 h-2 rounded-full bg-yellow-500 mt-2\" />\n            <div>\n              <p className=\"text-sm font-medium text-white\">Token Breakdown Estimation</p>\n              <p className=\"text-xs text-gray-400 mt-1\">\n                Input/output token breakdown is estimated based on typical usage patterns (~85% input, ~15% output). \n                For precise tracking, individual input/output tokens will be implemented in a future update.\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA8CO,MAAM,oBAAoB,CAAC,EAAE,IAAI,EAA0B;IAChE,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;YACL,MAAM;QACR,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,cAAc;IAC3B;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6VAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,2JAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,6VAAC,2JAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6VAAC,2JAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6VAAC,2JAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,6VAAC,2JAAA,CAAA,aAAU;gDAAC,OAAM;gDAAK,WAAU;0DAA6B;;;;;;0DAC9D,6VAAC,2JAAA,CAAA,aAAU;gDAAC,OAAM;gDAAM,WAAU;0DAA6B;;;;;;0DAC/D,6VAAC,2JAAA,CAAA,aAAU;gDAAC,OAAM;gDAAM,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAGnE,6VAAC,2JAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAOhG,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDACzD,6VAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;0CAEjB,6VAAC,yJAAA,CAAA,cAAW;;kDACV,6VAAC;wCAAI,WAAU;kDAAiC,aAAa,KAAK,WAAW;;;;;;kDAC7E,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAI9C,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDACzD,6VAAC,gSAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;0CAErB,6VAAC,yJAAA,CAAA,cAAW;;kDACV,6VAAC;wCAAI,WAAU;kDAAiC,aAAa,KAAK,oBAAoB;;;;;;kDACtF,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAI9C,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDACzD,6VAAC,oSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,6VAAC,yJAAA,CAAA,cAAW;;kDACV,6VAAC;wCAAI,WAAU;kDAAiC,aAAa,KAAK,qBAAqB;;;;;;kDACvF,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAI9C,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDACzD,6VAAC,sSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6VAAC,yJAAA,CAAA,cAAW;;kDACV,6VAAC;wCAAI,WAAU;kDAAiC,aAAa,KAAK,cAAc;;;;;;kDAChF,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAI9C,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAoC;;;;;;kDACzD,6VAAC,4SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,6VAAC,yJAAA,CAAA,cAAW;;kDACV,6VAAC;wCAAI,WAAU;kDAAiC,aAAa,KAAK,gBAAgB;;;;;;kDAClF,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAMhD,6VAAC,yJAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6VAAC,yJAAA,CAAA,aAAU;;0CACT,6VAAC,yJAAA,CAAA,YAAS;gCAAC,WAAU;0CAAa;;;;;;0CAClC,6VAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6VAAC,yJAAA,CAAA,cAAW;kCACV,cAAA,6VAAC,2LAAA,CAAA,aAAU;4BAAC,MAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;0BAKpC,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;;kDACT,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;kDAClC,6VAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6VAAC,yJAAA,CAAA,cAAW;0CACV,cAAA,6VAAC;oCAAI,WAAU;8CACZ,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC1C,6VAAC;4CAAwB,WAAU;;8DACjC,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAI,WAAU;;;;;;sEACf,6VAAC;;8EACC,6VAAC;oEAAE,WAAU;8EAAkC,MAAM,OAAO;;;;;;8EAC5D,6VAAC;oEAAE,WAAU;;wEAAyB,MAAM,QAAQ;wEAAC;wEAAa,MAAM,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;;8DAGrF,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAE,WAAU;sEAAkC,aAAa,MAAM,MAAM;;;;;;sEACxE,6VAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;2CAV/B,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;kCAmB/B,6VAAC,yJAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6VAAC,yJAAA,CAAA,aAAU;;kDACT,6VAAC,yJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;kDAClC,6VAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6VAAC,yJAAA,CAAA,cAAW;0CACV,cAAA,6VAAC;oCAAI,WAAU;8CACZ,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBACjC,6VAAC;4CAAmB,WAAU;;8DAC5B,6VAAC;;sEACC,6VAAC;4DAAE,WAAU;sEAAkC,MAAM,OAAO;;;;;;sEAC5D,6VAAC;4DAAE,WAAU;;gEACV,WAAW,MAAM,SAAS;gEAAE;gEAAI,MAAM,QAAQ;;;;;;;;;;;;;8DAGnD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAE,WAAU;sEAAkC,aAAa,MAAM,MAAM;;;;;;sEACxE,6VAAC;4DAAE,WAAU;;gEAAyB,MAAM,QAAQ;gEAAC;gEAAQ,MAAM,WAAW;gEAAC;;;;;;;;;;;;;;2CATzE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmB5B,6VAAC,yJAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6VAAC,yJAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;;;;;0CACf,6VAAC;;kDACC,6VAAC;wCAAE,WAAU;kDAAiC;;;;;;kDAC9C,6VAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD", "debugId": null}}]}