// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// @ts-nocheck
import type {
  QueryGenqlSelection,
  Query,
  MutationGenqlSelection,
  Mutation,
} from './schema'
import {
  linkTypeMap,
  createClient as createClientOriginal,
  generateGraphqlOperation,
  type FieldsSelection,
  type GraphqlOperation,
  type ClientOptions,
  GenqlError,
} from './runtime'
export type { FieldsSelection } from './runtime'
export { GenqlError }

import types from './types'
export {
  fragmentOn,
  fragmentOnRecursiveCollection,
  type QueryGenqlSelection,
} from './schema'
const typeMap = linkTypeMap(types as any)

export interface Client {
  query<R extends QueryGenqlSelection>(
    request: R & { __name?: string },
  ): Promise<FieldsSelection<Query, R>>

  mutation<
R extends Omit<MutationGenqlSelection, "transaction" | "transactionAsync"> & {
      transaction?: TransactionStatusGenqlSelection & {
        __args: Omit<
          NonNullable<MutationGenqlSelection["transaction"]>["__args"],
          "data"
        > & { data: Transaction | string };
      };
      transactionAsync?: {
        __args: Omit<
          NonNullable<MutationGenqlSelection["transactionAsync"]>["__args"],
          "data"
        > & { data: Transaction | string };
      };
    },
>(
    request: R & { __name?: string },
  ): Promise<FieldsSelection<Mutation, R>>
}

const createClient = function (options?: ClientOptions): Client {
  const { url, headers } = getStuffFromEnv(options)
  return createClientOriginal({
    url: url.toString(),
    ...options,
    headers: { ...options?.headers, ...headers },
    queryRoot: typeMap.Query!,
    mutationRoot: typeMap.Mutation!,
    subscriptionRoot: typeMap.Subscription!,
  }) as any
}

export const everything = {
  __scalar: true,
}

export type QueryResult<fields extends QueryGenqlSelection> = FieldsSelection<
  Query,
  fields
>
export const generateQueryOp: (
  fields: QueryGenqlSelection & { __name?: string },
) => GraphqlOperation = function (fields) {
  return generateGraphqlOperation('query', typeMap.Query!, fields as any)
}

export type MutationResult<fields extends MutationGenqlSelection> =
  FieldsSelection<Mutation, fields>
export const generateMutationOp: (
  fields: MutationGenqlSelection & { __name?: string },
) => GraphqlOperation = function (fields) {
  return generateGraphqlOperation('mutation', typeMap.Mutation!, fields as any)
}


export const getStuffFromEnv = (options) => {
    const defaultEnvVarPrefix = "BASEHUB";

    options = options || {};
    if (options.token === undefined) {
      options.token = undefined || null;
    }
    if (options.prefix === undefined) {
      options.prefix = undefined || null;
    }
    // we'll use the draft from .env if available
    if (!options.draft && true) {
      options.draft = true;
    }

    const buildEnvVarName = (name) => {
      let prefix = defaultEnvVarPrefix;
      if (options.prefix) {
        if (options.prefix.endsWith("_")) {
          options.prefix = options.prefix.slice(0, -1); // we remove the trailing _
        }
  
        if (options.prefix.endsWith(name)) {
          // remove the name from the prefix
          options.prefix = options.prefix.slice(0, -name.length);
        }
  
        // the user may include BASEHUB in their prefix...
        if (options.prefix.endsWith(defaultEnvVarPrefix)) {
          prefix = options.prefix;
        } else {
          // ... if they don't, we'll add it ourselves.
          prefix = `${options.prefix}_${defaultEnvVarPrefix}`;
        }
      }
      // this should result in something like <prefix>_BASEHUB_{TOKEN,REF,DRAFT} or BASEHUB_{TOKEN,REF,DRAFT}
      return `${prefix}_${name}`;
    };

    const getEnvVar = (name: EnvVarName) => {
      if (typeof process === 'undefined') {
        return undefined;
      }
      return process?.env?.[buildEnvVarName(name)];
    };

    const parsedDebugForcedURL = getEnvVar("DEBUG_FORCED_URL");
    const parsedBackwardsCompatURL = getEnvVar("URL");

    const backwardsCompatURL = parsedBackwardsCompatURL
      ? new URL(parsedBackwardsCompatURL)
      : undefined;


    const basehubUrl = new URL(
      parsedDebugForcedURL
        ? parsedDebugForcedURL
        : `https://api.basehub.com/graphql`
    );

    // These params can either come disambiguated, or in the URL.
    // Params that come from the URL take precedence.

    const parsedBasehubTokenEnv = getEnvVar("TOKEN");
    const parsedBasehubRefEnv = getEnvVar("REF");
    const parsedBasehubDraftEnv = getEnvVar("DRAFT");
    const parsedBasehubApiVersionEnv = getEnvVar("API_VERSION");

    let tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${buildEnvVarName(
      "TOKEN"
    )} env var.`;

    const resolveTokenParam = (token) => {
      if (!token) return null;
      const isRaw = token.startsWith("bshb_");
      if (isRaw) return token;
      tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${token} env var.`;
      if (typeof process === 'undefined') {
        return undefined;
      }
      return process?.env?.[token] ?? ''; // empty string to prevent fallback
    };

    const resolvedToken = resolveTokenParam(options?.token ?? null);

    const token =
      resolvedToken ?? basehubUrl.searchParams.get("token") ??
      parsedBasehubTokenEnv ??
      (backwardsCompatURL
        ? backwardsCompatURL.searchParams.get("token")
        : undefined) ??
      null;

    if (!token) {
      throw new Error(tokenNotFoundErrorMessage);
    }

    let draft =
       basehubUrl.searchParams.get("draft") ??
      parsedBasehubDraftEnv ??
      (backwardsCompatURL
        ? backwardsCompatURL.searchParams.get("draft")
        : undefined) ??
      false;

    if (options?.draft !== undefined) {
      draft = options.draft;
    }

    let apiVersion =
      basehubUrl.searchParams.get("api-version") ??
      parsedBasehubApiVersionEnv ??
      (backwardsCompatURL
        ? backwardsCompatURL.searchParams.get("api-version")
        : undefined) ??
      "4";

      if (options?.apiVersion !== undefined) {
        apiVersion = options.apiVersion;
      }
  
    // 2. let's validate the URL

    if (basehubUrl.pathname.split("/")[1] !== "graphql") {
        throw new Error(`🔴 Invalid URL. The URL needs to point your repo's GraphQL endpoint, so the pathname should end with /graphql.`);
    }

    // we'll pass these via headers
    basehubUrl.searchParams.delete("token");
    basehubUrl.searchParams.delete("ref");
    basehubUrl.searchParams.delete("draft");
    basehubUrl.searchParams.delete("api-version");

    // 3.
    const gitBranch = "main";
    const gitCommitSHA = "6e44eccee3dd16b02c9af69ee464418189cd0acc";

    const sdkBuildId = "bshb_sdk_621de0288c54e";

    return {
      isForcedDraft: true,
      draft,
      url: basehubUrl,
      sdkBuildId,
      headers: {
        "x-basehub-token": token,
        "x-basehub-ref": options?.ref ?? resolvedRef.ref,
        "x-basehub-sdk-build-id": sdkBuildId,
        ...(gitBranch ? { "x-basehub-git-branch": gitBranch } : {}),
        ...(gitCommitSHA ? { "x-basehub-git-commit-sha": gitCommitSHA } : {}),
        ...(gitBranchDeploymentURL ? { "x-basehub-git-branch-deployment-url": gitBranchDeploymentURL } : {}),
        ...(productionDeploymentURL ? { "x-basehub-production-deployment-url": productionDeploymentURL } : {}),
        ...(draft ? { "x-basehub-draft": "true" } : {}),
        ...(apiVersion ? { "x-basehub-api-version": apiVersion } : {}),
      },
    };
}
import type { Transaction } from './api-transaction';
import type { TransactionStatusGenqlSelection } from './schema';


export type * from "basehub/api-transaction";
import { createFetcher } from "./runtime";

export const sdkBuildId = "bshb_sdk_621de0288c54e";
export const resolvedRef = {"repoHash":"57ec52db","type":"branch","ref":"main","createSuggestedBranchLink":null,"id":"KluwvFPvKCxusUOmSQG4q","name":"main","git":null,"createdAt":"2025-06-16T00:30:26.760Z","archivedAt":null,"archivedBy":null,"headCommitId":"qNNz4p8JMipdRXk4579YJ","isDefault":true,"deletedAt":null,"workingRootBlockId":"a8Oul5Re6jsffvG4Ab5XZ"};
export const gitBranchDeploymentURL = null;
export const productionDeploymentURL = null;
export const isNextjs = true;

/**
 * Returns a hash code from an object
 * @param  {Object} obj The object to hash.
 * @return {String}    A hash string
 */
function hashObject(obj: Record<string, unknown>): string {
    const sortObjectKeys = <O extends Record<string, unknown>>(obj: O): O => {
        if (!isObjectAsWeCommonlyCallIt(obj)) return obj;
        return Object.keys(obj)
            .sort()
            .reduce((acc, key) => {
                acc[key as keyof O] = obj[key as keyof O];
                return acc;
            }, {} as O);
    };

    const recursiveSortObjectKeys = <O extends Record<string, unknown>>(obj: O): O => {
        const sortedObj = sortObjectKeys(obj);

        if (!isObjectAsWeCommonlyCallIt(sortedObj)) return sortedObj;

        Object.keys(sortedObj).forEach((key) => {
            if (isObjectAsWeCommonlyCallIt(sortedObj[key as keyof O])) {
                sortedObj[key as keyof O] = recursiveSortObjectKeys(
                    sortedObj[key as keyof O] as O
                ) as O[keyof O];
            } else if (Array.isArray(sortedObj[key as keyof O])) {
                sortedObj[key as keyof O] = (sortedObj[key as keyof O] as unknown[]).map(
                    (item) => {
                        if (isObjectAsWeCommonlyCallIt(item)) {
                            return recursiveSortObjectKeys(item);
                        } else {
                            return item;
                        }
                    }
                ) as O[keyof O];
            }
        });

        return sortedObj;
    };

    const isObjectAsWeCommonlyCallIt = (
        obj: unknown
    ): obj is Record<string, unknown> => {
        return Object.prototype.toString.call(obj) === '[object Object]';
    };

    const sortedObj = recursiveSortObjectKeys(obj);
    const str = JSON.stringify(sortedObj);

    let hash = 0;
    for (let i = 0, len = str.length; i < len; i++) {
        let chr = str.charCodeAt(i);
        hash = (hash << 5) - hash + chr;
        hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash).toString();
}

// we limit options to only the ones we want to expose.
type Options = Omit<ClientOptions, 'url' | 'method' | 'batch' | 'credentials' | 'fetch' | 'fetcher' | 'headers' | 'integrity' | 'keepalive' | 'mode' | 'redirect' | 'referrer' | 'referrerPolicy' | 'window'> & { draft?: boolean, token?: string, ref?: string }

// we include the resolvedRef.id to make sure the cache tag is unique per basehub ref
// solves a nice problem which we'd otherwise have, being that if the dev wants to hit a different basehub branch, we don't want to respond with the same cache tag as the previous branch
export function cacheTagFromQuery(query: QueryGenqlSelection, apiVersion: string | undefined) {
  const now = performance.now();
  const result = "basehub-" + hashObject({ ...query, refId: resolvedRef.id, ...(apiVersion ? { apiVersion } : {}) });
  return result;
}

/**
 * Create a basehub client.
 *
 * @param options (optional) Options for the `fetch` request; for example in Next.js, you can do `{ next: { revalidate: 60 } }` to tweak your app's cache.
 * @returns A basehub client.
 *
 * @example
 * import { basehub } from 'basehub'
 *
 * const firstQuery = await basehub().query({
 *   __typename: true,
 * });
 *
 * console.log(firstQuery.__typename) // => 'Query'
 *
 */
export const basehub = (options?: Options) => {
  const { url, headers } = getStuffFromEnv(options);

  if (!options) {
    options = {};
  }

  options.getExtraFetchOptions = async (op, _body, originalRequest) => {
    if (op !== 'query') return {}

    let extra = {
      headers: {
        "x-basehub-sdk-build-id": sdkBuildId,
      },
    };

    let isNextjsDraftMode = false;

    
      if (options.draft === undefined) {
        // try to auto-detect (only if draft is not explicitly set by the user)
        try {
          const { draftMode } = await import("next/headers");
          isNextjsDraftMode = (await draftMode()).isEnabled;
        } catch (error) {
          // noop, not using nextjs
        }
      }



    const isDraftResolved = true || isNextjsDraftMode || options.draft === true;

    if (isDraftResolved) {
      extra.headers = { ...extra.headers, "x-basehub-draft": "true" };

      
        // get rid of automatic nextjs caching
        extra.next = { revalidate: undefined };
        extra.cache = "no-store";
        // try to get ref from cookies
        try {
          const { cookies } = await import("next/headers");
          const cookieStore = await cookies();
          const ref = cookieStore.get("bshb-preview-ref-" + resolvedRef.repoHash)?.value;
          if (ref) {
            extra.headers = {
              ...extra.headers,
              "x-basehub-ref": ref,
            };
          }
        } catch (error) {
          // noop 
        }

    }

    if (isDraftResolved) return extra;

    
      if (typeof options?.next === 'undefined') {
        let isNextjs = false;
        try {
          isNextjs = !!(await import("next/headers"))
        } catch (error) {
          // noop, not using nextjs
        }
        if (isNextjs) {
          const cacheTag = cacheTagFromQuery(originalRequest, headers['x-basehub-api-version']);
          // don't override if revalidation is already being handled by the user
          extra.next = { tags: [cacheTag] };
          extra.headers = {
            ...extra.headers,
            "x-basehub-cache-tag": cacheTag,
          };
        }
      }
      

    return extra;
  }

  return {
    ...createClient(options),
    raw: createFetcher({ ...options, url, headers }) as <Cast = unknown>(
      gql: GraphqlOperation
    ) => Promise<Cast>,
  };
};

basehub.replaceSystemAliases = createClientOriginal.replaceSystemAliases;
